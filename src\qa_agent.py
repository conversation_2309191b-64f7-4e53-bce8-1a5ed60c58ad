"""
Comprehensive QA Agent for Aetherforge
Performs thorough testing of generated code, including unit tests, integration tests,
and validation against original requirements.
"""

import asyncio
import json
import logging
import os
import re
import subprocess
import tempfile
from typing import Dict, Any, List, Optional, Tuple, Union
from pathlib import Path
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum

# Import analyst and architect components for requirement validation
from src.analyst_agent import MC<PERSON>AGClient, ProjectSpecification
from src.architect_agent import SystemArchitecture
from src.developer_agent import ProjectStructure, CodeFile

logger = logging.getLogger(__name__)

class QAAgentError(Exception):
    """Base exception for QA Agent errors"""
    pass

class TestExecutionError(QAAgentError):
    """Raised when test execution fails"""
    pass

class ValidationError(QAAgentError):
    """Raised when validation fails"""
    pass

class CoverageError(QAAgentError):
    """Raised when coverage requirements are not met"""
    pass

class TestType(Enum):
    """Types of tests that can be executed"""
    UNIT = "unit"
    INTEGRATION = "integration"
    E2E = "e2e"
    API = "api"
    PERFORMANCE = "performance"
    SECURITY = "security"
    ACCESSIBILITY = "accessibility"

class TestStatus(Enum):
    """Test execution status"""
    PENDING = "pending"
    RUNNING = "running"
    PASSED = "passed"
    FAILED = "failed"
    SKIPPED = "skipped"
    ERROR = "error"

class QualityLevel(Enum):
    """Quality assurance levels"""
    BASIC = "basic"
    STANDARD = "standard"
    COMPREHENSIVE = "comprehensive"
    ENTERPRISE = "enterprise"

@dataclass
class TestResult:
    """Represents a test execution result"""
    test_name: str
    test_type: TestType
    status: TestStatus
    duration: float
    error_message: Optional[str] = None
    coverage: Optional[float] = None
    assertions: int = 0
    file_path: Optional[str] = None

@dataclass
class TestSuite:
    """Represents a collection of tests"""
    name: str
    test_type: TestType
    tests: List[TestResult] = field(default_factory=list)
    total_tests: int = 0
    passed_tests: int = 0
    failed_tests: int = 0
    skipped_tests: int = 0
    total_duration: float = 0.0
    coverage_percentage: float = 0.0

@dataclass
class QualityReport:
    """Comprehensive quality assessment report"""
    project_name: str
    test_suites: List[TestSuite] = field(default_factory=list)
    overall_coverage: float = 0.0
    quality_score: float = 0.0
    requirements_compliance: float = 0.0
    security_score: float = 0.0
    performance_score: float = 0.0
    issues_found: List[Dict[str, Any]] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())

@dataclass
class QAContext:
    """Context for QA execution"""
    project_path: Path
    project_specification: Optional[ProjectSpecification] = None
    architecture: Optional[SystemArchitecture] = None
    project_structure: Optional[ProjectStructure] = None
    quality_level: QualityLevel = QualityLevel.STANDARD
    requirements: Dict[str, Any] = field(default_factory=dict)
    test_config: Dict[str, Any] = field(default_factory=dict)

class QAAgent:
    """Comprehensive Quality Assurance Agent"""
    
    def __init__(self, mcp_url: str = None, openai_api_key: str = None):
        self.mcp_client = MCPRAGClient(mcp_url)
        self.openai_api_key = openai_api_key or os.getenv("OPENAI_API_KEY")
        
        # Test execution configurations
        self.test_frameworks = self._initialize_test_frameworks()
        self.quality_gates = self._initialize_quality_gates()
        self.validation_rules = self._initialize_validation_rules()
        
        # Coverage and quality thresholds
        self.coverage_thresholds = {
            QualityLevel.BASIC: 60.0,
            QualityLevel.STANDARD: 80.0,
            QualityLevel.COMPREHENSIVE: 90.0,
            QualityLevel.ENTERPRISE: 95.0
        }
    
    async def execute_qa_process(self, context: QAContext) -> QualityReport:
        """Execute comprehensive QA process"""
        logger.info(f"Starting QA process for project at {context.project_path}")
        
        try:
            # Step 1: Validate inputs and setup
            await self._validate_qa_inputs(context)
            
            # Step 2: Discover and analyze project structure
            await self._analyze_project_structure(context)
            
            # Step 3: Execute test suites
            test_suites = await self._execute_test_suites(context)
            
            # Step 4: Validate against requirements
            requirements_compliance = await self._validate_requirements_compliance(context)
            
            # Step 5: Perform security analysis
            security_score = await self._perform_security_analysis(context)
            
            # Step 6: Analyze performance characteristics
            performance_score = await self._analyze_performance(context)
            
            # Step 7: Calculate overall quality metrics
            quality_score = await self._calculate_quality_score(test_suites, requirements_compliance, security_score, performance_score)
            
            # Step 8: Generate recommendations
            recommendations = await self._generate_recommendations(context, test_suites)
            
            # Step 9: Create comprehensive report
            report = QualityReport(
                project_name=context.project_specification.project_name if context.project_specification else "Unknown Project",
                test_suites=test_suites,
                overall_coverage=self._calculate_overall_coverage(test_suites),
                quality_score=quality_score,
                requirements_compliance=requirements_compliance,
                security_score=security_score,
                performance_score=performance_score,
                recommendations=recommendations
            )
            
            # Step 10: Save report and artifacts
            await self._save_qa_artifacts(context, report)
            
            logger.info(f"QA process completed with quality score: {quality_score:.2f}")
            return report
            
        except Exception as e:
            logger.error(f"QA process failed: {e}")
            await self._handle_qa_error(e, context)
            raise QAAgentError(f"QA process failed: {e}")
    
    def _initialize_test_frameworks(self) -> Dict[str, Dict[str, Any]]:
        """Initialize test framework configurations"""
        return {
            "jest": {
                "command": "npm test",
                "coverage_command": "npm run test:coverage",
                "config_file": "jest.config.js",
                "test_pattern": "**/*.test.{js,ts,jsx,tsx}",
                "coverage_threshold": 80
            },
            "playwright": {
                "command": "npx playwright test",
                "config_file": "playwright.config.ts",
                "test_pattern": "**/*.spec.{js,ts}",
                "browsers": ["chromium", "firefox", "webkit"]
            },
            "cypress": {
                "command": "npx cypress run",
                "config_file": "cypress.config.js",
                "test_pattern": "cypress/e2e/**/*.cy.{js,ts}"
            },
            "pytest": {
                "command": "pytest",
                "coverage_command": "pytest --cov",
                "config_file": "pytest.ini",
                "test_pattern": "test_*.py"
            }
        }
    
    def _initialize_quality_gates(self) -> Dict[str, Dict[str, Any]]:
        """Initialize quality gate configurations"""
        return {
            QualityLevel.BASIC.value: {
                "min_coverage": 60.0,
                "max_critical_issues": 5,
                "max_high_issues": 10,
                "required_tests": ["unit"]
            },
            QualityLevel.STANDARD.value: {
                "min_coverage": 80.0,
                "max_critical_issues": 2,
                "max_high_issues": 5,
                "required_tests": ["unit", "integration"]
            },
            QualityLevel.COMPREHENSIVE.value: {
                "min_coverage": 90.0,
                "max_critical_issues": 0,
                "max_high_issues": 2,
                "required_tests": ["unit", "integration", "e2e"]
            },
            QualityLevel.ENTERPRISE.value: {
                "min_coverage": 95.0,
                "max_critical_issues": 0,
                "max_high_issues": 0,
                "required_tests": ["unit", "integration", "e2e", "performance", "security"]
            }
        }
    
    def _initialize_validation_rules(self) -> Dict[str, List[str]]:
        """Initialize validation rules for different aspects"""
        return {
            "code_quality": [
                "No syntax errors",
                "Consistent code style",
                "Proper error handling",
                "Appropriate logging",
                "Security best practices"
            ],
            "test_quality": [
                "Test coverage meets threshold",
                "Tests are independent",
                "Tests have clear assertions",
                "Edge cases are covered",
                "Mock usage is appropriate"
            ],
            "architecture": [
                "Follows specified architecture pattern",
                "Proper separation of concerns",
                "Dependency injection used correctly",
                "Configuration externalized",
                "Scalability considerations"
            ],
            "security": [
                "Input validation implemented",
                "Authentication/authorization present",
                "Sensitive data protection",
                "HTTPS configuration",
                "Security headers configured"
            ]
        }

    async def _validate_qa_inputs(self, context: QAContext):
        """Validate QA inputs and setup"""
        if not context.project_path.exists():
            raise ValidationError(f"Project path does not exist: {context.project_path}")

        # Check for package.json or requirements.txt
        has_package_json = (context.project_path / "package.json").exists()
        has_requirements = (context.project_path / "requirements.txt").exists()

        if not has_package_json and not has_requirements:
            raise ValidationError("No package.json or requirements.txt found")

        logger.info("QA input validation passed")

    async def _analyze_project_structure(self, context: QAContext):
        """Analyze project structure and identify test files"""
        project_files = []
        test_files = []

        for file_path in context.project_path.rglob("*"):
            if file_path.is_file():
                if any(pattern in file_path.name for pattern in ["test", "spec"]):
                    test_files.append(file_path)
                elif file_path.suffix in [".js", ".ts", ".jsx", ".tsx", ".py"]:
                    project_files.append(file_path)

        context.test_config["project_files"] = project_files
        context.test_config["test_files"] = test_files

        logger.info(f"Found {len(project_files)} source files and {len(test_files)} test files")

    async def _execute_test_suites(self, context: QAContext) -> List[TestSuite]:
        """Execute all test suites"""
        test_suites = []

        # Execute unit tests
        unit_suite = await self._execute_unit_tests(context)
        if unit_suite:
            test_suites.append(unit_suite)

        # Execute integration tests
        integration_suite = await self._execute_integration_tests(context)
        if integration_suite:
            test_suites.append(integration_suite)

        # Execute E2E tests if available
        e2e_suite = await self._execute_e2e_tests(context)
        if e2e_suite:
            test_suites.append(e2e_suite)

        # Execute API tests if applicable
        api_suite = await self._execute_api_tests(context)
        if api_suite:
            test_suites.append(api_suite)

        return test_suites

    async def _execute_unit_tests(self, context: QAContext) -> Optional[TestSuite]:
        """Execute unit tests using appropriate framework"""
        logger.info("Executing unit tests...")

        # Detect test framework
        framework = self._detect_test_framework(context.project_path)
        if not framework:
            logger.warning("No test framework detected")
            return None

        framework_config = self.test_frameworks[framework]

        try:
            # Run tests with coverage
            result = await self._run_test_command(
                context.project_path,
                framework_config.get("coverage_command", framework_config["command"])
            )

            # Parse test results
            test_results = await self._parse_test_results(result, TestType.UNIT)

            # Calculate coverage
            coverage = await self._extract_coverage_info(result, context.project_path)

            suite = TestSuite(
                name="Unit Tests",
                test_type=TestType.UNIT,
                tests=test_results,
                total_tests=len(test_results),
                passed_tests=len([t for t in test_results if t.status == TestStatus.PASSED]),
                failed_tests=len([t for t in test_results if t.status == TestStatus.FAILED]),
                skipped_tests=len([t for t in test_results if t.status == TestStatus.SKIPPED]),
                total_duration=sum(t.duration for t in test_results),
                coverage_percentage=coverage
            )

            logger.info(f"Unit tests completed: {suite.passed_tests}/{suite.total_tests} passed")
            return suite

        except Exception as e:
            logger.error(f"Unit test execution failed: {e}")
            return TestSuite(
                name="Unit Tests",
                test_type=TestType.UNIT,
                tests=[TestResult(
                    test_name="Unit Test Execution",
                    test_type=TestType.UNIT,
                    status=TestStatus.ERROR,
                    duration=0.0,
                    error_message=str(e)
                )]
            )

    async def _execute_integration_tests(self, context: QAContext) -> Optional[TestSuite]:
        """Execute integration tests"""
        logger.info("Executing integration tests...")

        # Look for integration test files
        integration_files = [
            f for f in context.test_config.get("test_files", [])
            if "integration" in str(f).lower()
        ]

        if not integration_files:
            logger.info("No integration tests found")
            return None

        try:
            # Run integration tests
            result = await self._run_test_command(
                context.project_path,
                "npm test -- --testPathPattern=integration"
            )

            test_results = await self._parse_test_results(result, TestType.INTEGRATION)

            suite = TestSuite(
                name="Integration Tests",
                test_type=TestType.INTEGRATION,
                tests=test_results,
                total_tests=len(test_results),
                passed_tests=len([t for t in test_results if t.status == TestStatus.PASSED]),
                failed_tests=len([t for t in test_results if t.status == TestStatus.FAILED]),
                total_duration=sum(t.duration for t in test_results)
            )

            logger.info(f"Integration tests completed: {suite.passed_tests}/{suite.total_tests} passed")
            return suite

        except Exception as e:
            logger.error(f"Integration test execution failed: {e}")
            return None

    async def _execute_e2e_tests(self, context: QAContext) -> Optional[TestSuite]:
        """Execute end-to-end tests"""
        logger.info("Executing E2E tests...")

        # Check for Playwright or Cypress configuration
        has_playwright = (context.project_path / "playwright.config.ts").exists()
        has_cypress = (context.project_path / "cypress.config.js").exists()

        if not has_playwright and not has_cypress:
            logger.info("No E2E test framework detected")
            return None

        try:
            if has_playwright:
                result = await self._run_test_command(context.project_path, "npx playwright test")
            else:
                result = await self._run_test_command(context.project_path, "npx cypress run")

            test_results = await self._parse_test_results(result, TestType.E2E)

            suite = TestSuite(
                name="End-to-End Tests",
                test_type=TestType.E2E,
                tests=test_results,
                total_tests=len(test_results),
                passed_tests=len([t for t in test_results if t.status == TestStatus.PASSED]),
                failed_tests=len([t for t in test_results if t.status == TestStatus.FAILED]),
                total_duration=sum(t.duration for t in test_results)
            )

            logger.info(f"E2E tests completed: {suite.passed_tests}/{suite.total_tests} passed")
            return suite

        except Exception as e:
            logger.error(f"E2E test execution failed: {e}")
            return None

    async def _execute_api_tests(self, context: QAContext) -> Optional[TestSuite]:
        """Execute API tests"""
        logger.info("Executing API tests...")

        # Look for API test files or server files
        has_server = any(
            "server" in str(f).lower() or "app" in str(f).lower()
            for f in context.test_config.get("project_files", [])
        )

        if not has_server:
            logger.info("No API server detected")
            return None

        try:
            # Run API tests (typically integration tests for APIs)
            result = await self._run_test_command(
                context.project_path,
                "npm test -- --testPathPattern=api"
            )

            test_results = await self._parse_test_results(result, TestType.API)

            suite = TestSuite(
                name="API Tests",
                test_type=TestType.API,
                tests=test_results,
                total_tests=len(test_results),
                passed_tests=len([t for t in test_results if t.status == TestStatus.PASSED]),
                failed_tests=len([t for t in test_results if t.status == TestStatus.FAILED]),
                total_duration=sum(t.duration for t in test_results)
            )

            logger.info(f"API tests completed: {suite.passed_tests}/{suite.total_tests} passed")
            return suite

        except Exception as e:
            logger.error(f"API test execution failed: {e}")
            return None

    async def _validate_requirements_compliance(self, context: QAContext) -> float:
        """Validate compliance with original requirements"""
        logger.info("Validating requirements compliance...")

        if not context.project_specification:
            logger.warning("No project specification available for validation")
            return 0.0

        compliance_score = 0.0
        total_checks = 0

        try:
            # Check functional requirements
            functional_score = await self._check_functional_requirements(context)
            compliance_score += functional_score
            total_checks += 1

            # Check non-functional requirements
            nonfunctional_score = await self._check_nonfunctional_requirements(context)
            compliance_score += nonfunctional_score
            total_checks += 1

            # Check user stories implementation
            user_stories_score = await self._check_user_stories_implementation(context)
            compliance_score += user_stories_score
            total_checks += 1

            final_score = compliance_score / total_checks if total_checks > 0 else 0.0
            logger.info(f"Requirements compliance score: {final_score:.2f}")
            return final_score

        except Exception as e:
            logger.error(f"Requirements validation failed: {e}")
            return 0.0

    async def _perform_security_analysis(self, context: QAContext) -> float:
        """Perform security analysis of the generated code"""
        logger.info("Performing security analysis...")

        security_score = 0.0
        total_checks = 0

        try:
            # Check for common security vulnerabilities
            vuln_score = await self._check_security_vulnerabilities(context)
            security_score += vuln_score
            total_checks += 1

            # Check authentication/authorization implementation
            auth_score = await self._check_authentication_implementation(context)
            security_score += auth_score
            total_checks += 1

            # Check input validation
            validation_score = await self._check_input_validation(context)
            security_score += validation_score
            total_checks += 1

            # Check HTTPS and security headers
            headers_score = await self._check_security_headers(context)
            security_score += headers_score
            total_checks += 1

            final_score = security_score / total_checks if total_checks > 0 else 0.0
            logger.info(f"Security score: {final_score:.2f}")
            return final_score

        except Exception as e:
            logger.error(f"Security analysis failed: {e}")
            return 0.0

    async def _analyze_performance(self, context: QAContext) -> float:
        """Analyze performance characteristics"""
        logger.info("Analyzing performance...")

        performance_score = 0.0
        total_checks = 0

        try:
            # Check for performance best practices
            practices_score = await self._check_performance_practices(context)
            performance_score += practices_score
            total_checks += 1

            # Analyze bundle size (for frontend projects)
            bundle_score = await self._analyze_bundle_size(context)
            performance_score += bundle_score
            total_checks += 1

            # Check database query optimization (if applicable)
            db_score = await self._check_database_optimization(context)
            performance_score += db_score
            total_checks += 1

            final_score = performance_score / total_checks if total_checks > 0 else 0.0
            logger.info(f"Performance score: {final_score:.2f}")
            return final_score

        except Exception as e:
            logger.error(f"Performance analysis failed: {e}")
            return 0.0

    async def _calculate_quality_score(self, test_suites: List[TestSuite],
                                     requirements_compliance: float,
                                     security_score: float,
                                     performance_score: float) -> float:
        """Calculate overall quality score"""
        # Weight factors for different aspects
        weights = {
            "test_coverage": 0.3,
            "test_success": 0.2,
            "requirements": 0.2,
            "security": 0.15,
            "performance": 0.15
        }

        # Calculate test metrics
        total_tests = sum(suite.total_tests for suite in test_suites)
        passed_tests = sum(suite.passed_tests for suite in test_suites)
        overall_coverage = self._calculate_overall_coverage(test_suites)

        test_success_rate = passed_tests / total_tests if total_tests > 0 else 0.0
        coverage_score = min(overall_coverage / 100.0, 1.0)

        # Calculate weighted score
        quality_score = (
            weights["test_coverage"] * coverage_score +
            weights["test_success"] * test_success_rate +
            weights["requirements"] * requirements_compliance +
            weights["security"] * security_score +
            weights["performance"] * performance_score
        ) * 100.0

        return min(quality_score, 100.0)

    def _calculate_overall_coverage(self, test_suites: List[TestSuite]) -> float:
        """Calculate overall test coverage"""
        coverage_values = [suite.coverage_percentage for suite in test_suites if suite.coverage_percentage > 0]
        return sum(coverage_values) / len(coverage_values) if coverage_values else 0.0

    async def _generate_recommendations(self, context: QAContext, test_suites: List[TestSuite]) -> List[str]:
        """Generate improvement recommendations"""
        recommendations = []

        # Coverage recommendations
        overall_coverage = self._calculate_overall_coverage(test_suites)
        threshold = self.coverage_thresholds[context.quality_level]

        if overall_coverage < threshold:
            recommendations.append(f"Increase test coverage from {overall_coverage:.1f}% to at least {threshold}%")

        # Failed test recommendations
        for suite in test_suites:
            if suite.failed_tests > 0:
                recommendations.append(f"Fix {suite.failed_tests} failing {suite.test_type.value} tests")

        # Security recommendations
        recommendations.extend(await self._get_security_recommendations(context))

        # Performance recommendations
        recommendations.extend(await self._get_performance_recommendations(context))

        return recommendations

    def _detect_test_framework(self, project_path: Path) -> Optional[str]:
        """Detect the test framework used in the project"""
        package_json = project_path / "package.json"

        if package_json.exists():
            try:
                with open(package_json, 'r') as f:
                    package_data = json.load(f)

                dependencies = {**package_data.get("dependencies", {}), **package_data.get("devDependencies", {})}

                if "jest" in dependencies:
                    return "jest"
                elif "playwright" in dependencies:
                    return "playwright"
                elif "cypress" in dependencies:
                    return "cypress"
            except Exception as e:
                logger.warning(f"Failed to parse package.json: {e}")

        # Check for Python projects
        if (project_path / "requirements.txt").exists() or (project_path / "pyproject.toml").exists():
            return "pytest"

        return None

    async def _run_test_command(self, project_path: Path, command: str) -> Dict[str, Any]:
        """Run a test command and capture output"""
        logger.info(f"Running command: {command}")

        try:
            process = await asyncio.create_subprocess_shell(
                command,
                cwd=project_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            return {
                "returncode": process.returncode,
                "stdout": stdout.decode('utf-8'),
                "stderr": stderr.decode('utf-8'),
                "command": command
            }
        except Exception as e:
            logger.error(f"Command execution failed: {e}")
            return {
                "returncode": 1,
                "stdout": "",
                "stderr": str(e),
                "command": command
            }

    async def _parse_test_results(self, command_result: Dict[str, Any], test_type: TestType) -> List[TestResult]:
        """Parse test results from command output"""
        test_results = []

        if command_result["returncode"] != 0:
            # Command failed
            test_results.append(TestResult(
                test_name="Test Execution",
                test_type=test_type,
                status=TestStatus.ERROR,
                duration=0.0,
                error_message=command_result["stderr"]
            ))
            return test_results

        stdout = command_result["stdout"]

        # Parse Jest output
        if "jest" in command_result["command"].lower():
            test_results.extend(self._parse_jest_output(stdout, test_type))

        # Parse Playwright output
        elif "playwright" in command_result["command"].lower():
            test_results.extend(self._parse_playwright_output(stdout, test_type))

        # Parse Cypress output
        elif "cypress" in command_result["command"].lower():
            test_results.extend(self._parse_cypress_output(stdout, test_type))

        # Default parsing for unknown formats
        else:
            test_results.extend(self._parse_generic_output(stdout, test_type))

        return test_results

    def _parse_jest_output(self, output: str, test_type: TestType) -> List[TestResult]:
        """Parse Jest test output"""
        test_results = []

        # Look for test results in Jest format
        test_pattern = r'(PASS|FAIL)\s+(.+?)\s+\((\d+(?:\.\d+)?)\s*s\)'
        matches = re.findall(test_pattern, output)

        for status_str, test_name, duration_str in matches:
            status = TestStatus.PASSED if status_str == "PASS" else TestStatus.FAILED
            duration = float(duration_str)

            test_results.append(TestResult(
                test_name=test_name,
                test_type=test_type,
                status=status,
                duration=duration
            ))

        return test_results

    def _parse_playwright_output(self, output: str, test_type: TestType) -> List[TestResult]:
        """Parse Playwright test output"""
        test_results = []

        # Look for Playwright test results
        test_pattern = r'(\d+)\s+passed.*?(\d+)\s+failed'
        match = re.search(test_pattern, output)

        if match:
            passed_count = int(match.group(1))
            failed_count = int(match.group(2))

            # Create summary results
            for i in range(passed_count):
                test_results.append(TestResult(
                    test_name=f"E2E Test {i+1}",
                    test_type=test_type,
                    status=TestStatus.PASSED,
                    duration=1.0  # Default duration
                ))

            for i in range(failed_count):
                test_results.append(TestResult(
                    test_name=f"E2E Test {passed_count + i + 1}",
                    test_type=test_type,
                    status=TestStatus.FAILED,
                    duration=1.0
                ))

        return test_results

    def _parse_cypress_output(self, output: str, test_type: TestType) -> List[TestResult]:
        """Parse Cypress test output"""
        test_results = []

        # Look for Cypress test results
        test_pattern = r'(\d+)\s+passing.*?(\d+)\s+failing'
        match = re.search(test_pattern, output)

        if match:
            passed_count = int(match.group(1))
            failed_count = int(match.group(2))

            for i in range(passed_count):
                test_results.append(TestResult(
                    test_name=f"Cypress Test {i+1}",
                    test_type=test_type,
                    status=TestStatus.PASSED,
                    duration=1.0
                ))

            for i in range(failed_count):
                test_results.append(TestResult(
                    test_name=f"Cypress Test {passed_count + i + 1}",
                    test_type=test_type,
                    status=TestStatus.FAILED,
                    duration=1.0
                ))

        return test_results

    def _parse_generic_output(self, output: str, test_type: TestType) -> List[TestResult]:
        """Parse generic test output"""
        # Create a basic result based on command success
        return [TestResult(
            test_name="Generic Test",
            test_type=test_type,
            status=TestStatus.PASSED,
            duration=1.0
        )]

    async def _extract_coverage_info(self, command_result: Dict[str, Any], project_path: Path) -> float:
        """Extract coverage information from test output"""
        stdout = command_result["stdout"]

        # Look for coverage percentage in output
        coverage_patterns = [
            r'All files\s+\|\s+(\d+(?:\.\d+)?)',  # Jest coverage
            r'Statements\s+:\s+(\d+(?:\.\d+)?)%',  # Istanbul coverage
            r'Coverage:\s+(\d+(?:\.\d+)?)%'        # Generic coverage
        ]

        for pattern in coverage_patterns:
            match = re.search(pattern, stdout)
            if match:
                return float(match.group(1))

        # Look for coverage files
        coverage_files = [
            project_path / "coverage" / "lcov-report" / "index.html",
            project_path / "coverage" / "coverage-summary.json"
        ]

        for coverage_file in coverage_files:
            if coverage_file.exists():
                try:
                    if coverage_file.suffix == ".json":
                        with open(coverage_file, 'r') as f:
                            coverage_data = json.load(f)
                            return coverage_data.get("total", {}).get("lines", {}).get("pct", 0.0)
                except Exception as e:
                    logger.warning(f"Failed to parse coverage file: {e}")

        return 0.0

    async def _check_functional_requirements(self, context: QAContext) -> float:
        """Check if functional requirements are implemented"""
        if not context.project_specification:
            return 0.0

        # Get functional requirements from the requirements dict
        functional_reqs = context.project_specification.requirements.get("functional", [])
        if not functional_reqs:
            return 0.0

        # Analyze source code for requirement implementation
        implemented_count = 0
        total_requirements = len(functional_reqs)

        for requirement in functional_reqs:
            if await self._check_requirement_implementation(requirement, context):
                implemented_count += 1

        return implemented_count / total_requirements if total_requirements > 0 else 0.0

    async def _check_requirement_implementation(self, requirement: str, context: QAContext) -> bool:
        """Check if a specific requirement is implemented"""
        # Simple keyword-based check in source files
        keywords = self._extract_keywords_from_requirement(requirement)

        for file_path in context.test_config.get("project_files", []):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read().lower()
                    if any(keyword.lower() in content for keyword in keywords):
                        return True
            except Exception:
                continue

        return False

    def _extract_keywords_from_requirement(self, requirement: str) -> List[str]:
        """Extract keywords from a requirement description"""
        # Simple keyword extraction
        common_words = {"the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by"}
        words = re.findall(r'\b\w+\b', requirement.lower())
        return [word for word in words if len(word) > 3 and word not in common_words]

    async def _check_nonfunctional_requirements(self, context: QAContext) -> float:
        """Check non-functional requirements compliance"""
        score = 0.0
        checks = 0

        # Check performance requirements
        if await self._has_performance_optimizations(context):
            score += 1.0
        checks += 1

        # Check security requirements
        if await self._has_security_measures(context):
            score += 1.0
        checks += 1

        # Check scalability considerations
        if await self._has_scalability_features(context):
            score += 1.0
        checks += 1

        return score / checks if checks > 0 else 0.0

    async def _check_user_stories_implementation(self, context: QAContext) -> float:
        """Check if user stories are implemented"""
        if not context.project_specification or not context.project_specification.user_stories:
            return 0.0

        implemented_count = 0
        total_stories = len(context.project_specification.user_stories)

        for story in context.project_specification.user_stories:
            if await self._check_user_story_implementation(story, context):
                implemented_count += 1

        return implemented_count / total_stories if total_stories > 0 else 0.0

    async def _check_user_story_implementation(self, story: Dict[str, Any], context: QAContext) -> bool:
        """Check if a user story is implemented"""
        # Extract key actions and features from user story
        story_text = story.get("description", "")
        keywords = self._extract_keywords_from_requirement(story_text)

        # Check for implementation in source files
        for file_path in context.test_config.get("project_files", []):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read().lower()
                    if any(keyword.lower() in content for keyword in keywords):
                        return True
            except Exception:
                continue

        return False

    async def _check_security_vulnerabilities(self, context: QAContext) -> float:
        """Check for common security vulnerabilities"""
        vulnerabilities_found = 0
        total_checks = 0

        # Check for SQL injection vulnerabilities
        if await self._check_sql_injection_protection(context):
            vulnerabilities_found += 1
        total_checks += 1

        # Check for XSS protection
        if await self._check_xss_protection(context):
            vulnerabilities_found += 1
        total_checks += 1

        # Check for CSRF protection
        if await self._check_csrf_protection(context):
            vulnerabilities_found += 1
        total_checks += 1

        return vulnerabilities_found / total_checks if total_checks > 0 else 0.0

    async def _check_authentication_implementation(self, context: QAContext) -> float:
        """Check authentication implementation"""
        auth_features = 0
        total_features = 0

        # Check for JWT implementation
        if await self._has_jwt_implementation(context):
            auth_features += 1
        total_features += 1

        # Check for password hashing
        if await self._has_password_hashing(context):
            auth_features += 1
        total_features += 1

        # Check for session management
        if await self._has_session_management(context):
            auth_features += 1
        total_features += 1

        return auth_features / total_features if total_features > 0 else 0.0

    async def _check_input_validation(self, context: QAContext) -> float:
        """Check input validation implementation"""
        validation_patterns = [
            r'validate\(',
            r'joi\.',
            r'yup\.',
            r'zod\.',
            r'express-validator'
        ]

        validation_found = False
        for file_path in context.test_config.get("project_files", []):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if any(re.search(pattern, content, re.IGNORECASE) for pattern in validation_patterns):
                        validation_found = True
                        break
            except Exception:
                continue

        return 1.0 if validation_found else 0.0

    async def _check_security_headers(self, context: QAContext) -> float:
        """Check for security headers implementation"""
        security_headers = [
            "helmet",
            "cors",
            "x-frame-options",
            "content-security-policy",
            "x-xss-protection"
        ]

        headers_found = 0
        for file_path in context.test_config.get("project_files", []):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read().lower()
                    for header in security_headers:
                        if header in content:
                            headers_found += 1
                            break
            except Exception:
                continue

        return min(headers_found / len(security_headers), 1.0)

    async def _check_performance_practices(self, context: QAContext) -> float:
        """Check for performance best practices"""
        practices_score = 0.0
        total_practices = 0

        # Check for caching implementation
        if await self._has_caching_implementation(context):
            practices_score += 1.0
        total_practices += 1

        # Check for compression
        if await self._has_compression_enabled(context):
            practices_score += 1.0
        total_practices += 1

        # Check for lazy loading
        if await self._has_lazy_loading(context):
            practices_score += 1.0
        total_practices += 1

        return practices_score / total_practices if total_practices > 0 else 0.0

    async def _analyze_bundle_size(self, context: QAContext) -> float:
        """Analyze frontend bundle size"""
        # Check for webpack or build configuration
        build_files = [
            context.project_path / "webpack.config.js",
            context.project_path / "vite.config.js",
            context.project_path / "next.config.js"
        ]

        has_build_optimization = any(f.exists() for f in build_files)
        return 1.0 if has_build_optimization else 0.5

    async def _check_database_optimization(self, context: QAContext) -> float:
        """Check database optimization"""
        db_patterns = [
            r'index\s*\(',
            r'\.index\(',
            r'createIndex',
            r'ensureIndex',
            r'query\s*optimization'
        ]

        optimization_found = False
        for file_path in context.test_config.get("project_files", []):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if any(re.search(pattern, content, re.IGNORECASE) for pattern in db_patterns):
                        optimization_found = True
                        break
            except Exception:
                continue

        return 1.0 if optimization_found else 0.5

    # Helper methods for specific checks
    async def _has_performance_optimizations(self, context: QAContext) -> bool:
        """Check if performance optimizations are present"""
        perf_keywords = ["cache", "optimize", "performance", "lazy", "compression"]
        return await self._check_keywords_in_files(context, perf_keywords)

    async def _has_security_measures(self, context: QAContext) -> bool:
        """Check if security measures are implemented"""
        security_keywords = ["helmet", "cors", "auth", "jwt", "bcrypt", "hash"]
        return await self._check_keywords_in_files(context, security_keywords)

    async def _has_scalability_features(self, context: QAContext) -> bool:
        """Check if scalability features are present"""
        scalability_keywords = ["cluster", "worker", "pool", "queue", "cache", "redis"]
        return await self._check_keywords_in_files(context, scalability_keywords)

    async def _check_keywords_in_files(self, context: QAContext, keywords: List[str]) -> bool:
        """Check if any keywords are present in project files"""
        for file_path in context.test_config.get("project_files", []):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read().lower()
                    if any(keyword.lower() in content for keyword in keywords):
                        return True
            except Exception:
                continue
        return False

    # Additional security check methods
    async def _check_sql_injection_protection(self, context: QAContext) -> bool:
        """Check for SQL injection protection"""
        protection_patterns = ["parameterized", "prepared", "sanitize", "escape"]
        return await self._check_keywords_in_files(context, protection_patterns)

    async def _check_xss_protection(self, context: QAContext) -> bool:
        """Check for XSS protection"""
        xss_patterns = ["helmet", "xss", "sanitize", "escape", "content-security-policy"]
        return await self._check_keywords_in_files(context, xss_patterns)

    async def _check_csrf_protection(self, context: QAContext) -> bool:
        """Check for CSRF protection"""
        csrf_patterns = ["csrf", "csurf", "token", "anti-forgery"]
        return await self._check_keywords_in_files(context, csrf_patterns)

    async def _has_jwt_implementation(self, context: QAContext) -> bool:
        """Check for JWT implementation"""
        jwt_patterns = ["jwt", "jsonwebtoken", "bearer", "token"]
        return await self._check_keywords_in_files(context, jwt_patterns)

    async def _has_password_hashing(self, context: QAContext) -> bool:
        """Check for password hashing"""
        hash_patterns = ["bcrypt", "hash", "salt", "crypto"]
        return await self._check_keywords_in_files(context, hash_patterns)

    async def _has_session_management(self, context: QAContext) -> bool:
        """Check for session management"""
        session_patterns = ["session", "cookie", "express-session"]
        return await self._check_keywords_in_files(context, session_patterns)

    async def _has_caching_implementation(self, context: QAContext) -> bool:
        """Check for caching implementation"""
        cache_patterns = ["cache", "redis", "memcached", "lru"]
        return await self._check_keywords_in_files(context, cache_patterns)

    async def _has_compression_enabled(self, context: QAContext) -> bool:
        """Check for compression"""
        compression_patterns = ["compression", "gzip", "deflate"]
        return await self._check_keywords_in_files(context, compression_patterns)

    async def _has_lazy_loading(self, context: QAContext) -> bool:
        """Check for lazy loading"""
        lazy_patterns = ["lazy", "dynamic import", "code splitting"]
        return await self._check_keywords_in_files(context, lazy_patterns)

    async def _get_security_recommendations(self, context: QAContext) -> List[str]:
        """Get security-specific recommendations"""
        recommendations = []

        if not await self._has_security_measures(context):
            recommendations.append("Implement security headers using helmet.js")

        if not await self._has_jwt_implementation(context):
            recommendations.append("Add JWT-based authentication")

        if not await self._check_input_validation(context):
            recommendations.append("Implement input validation for all endpoints")

        return recommendations

    async def _get_performance_recommendations(self, context: QAContext) -> List[str]:
        """Get performance-specific recommendations"""
        recommendations = []

        if not await self._has_caching_implementation(context):
            recommendations.append("Implement caching strategy for improved performance")

        if not await self._has_compression_enabled(context):
            recommendations.append("Enable compression middleware")

        if not await self._has_lazy_loading(context):
            recommendations.append("Implement lazy loading for better initial load times")

        return recommendations

    async def _save_qa_artifacts(self, context: QAContext, report: QualityReport):
        """Save QA artifacts and reports"""
        qa_dir = context.project_path / "qa_reports"
        qa_dir.mkdir(exist_ok=True)

        # Save quality report
        report_file = qa_dir / f"quality_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump({
                "project_name": report.project_name,
                "overall_coverage": report.overall_coverage,
                "quality_score": report.quality_score,
                "requirements_compliance": report.requirements_compliance,
                "security_score": report.security_score,
                "performance_score": report.performance_score,
                "test_suites": [
                    {
                        "name": suite.name,
                        "test_type": suite.test_type.value,
                        "total_tests": suite.total_tests,
                        "passed_tests": suite.passed_tests,
                        "failed_tests": suite.failed_tests,
                        "coverage_percentage": suite.coverage_percentage
                    }
                    for suite in report.test_suites
                ],
                "recommendations": report.recommendations,
                "timestamp": report.timestamp
            }, f, indent=2)

        # Save detailed test results
        for suite in report.test_suites:
            suite_file = qa_dir / f"{suite.test_type.value}_results.json"
            with open(suite_file, 'w') as f:
                json.dump({
                    "suite_name": suite.name,
                    "test_type": suite.test_type.value,
                    "summary": {
                        "total_tests": suite.total_tests,
                        "passed_tests": suite.passed_tests,
                        "failed_tests": suite.failed_tests,
                        "skipped_tests": suite.skipped_tests,
                        "total_duration": suite.total_duration,
                        "coverage_percentage": suite.coverage_percentage
                    },
                    "tests": [
                        {
                            "test_name": test.test_name,
                            "status": test.status.value,
                            "duration": test.duration,
                            "error_message": test.error_message,
                            "file_path": test.file_path
                        }
                        for test in suite.tests
                    ]
                }, f, indent=2)

        logger.info(f"QA artifacts saved to {qa_dir}")

    async def _handle_qa_error(self, error: Exception, context: QAContext):
        """Handle QA process errors"""
        logger.error(f"QA process error: {error}")

        # Create error report
        error_report = {
            "timestamp": datetime.now().isoformat(),
            "project_path": str(context.project_path),
            "error_type": type(error).__name__,
            "error_message": str(error),
            "quality_level": context.quality_level.value
        }

        # Save error report
        try:
            qa_dir = context.project_path / "qa_reports"
            qa_dir.mkdir(exist_ok=True)
            error_file = qa_dir / "qa_error.json"
            with open(error_file, 'w') as f:
                json.dump(error_report, f, indent=2)
            logger.info(f"Error report saved to {error_file}")
        except Exception as save_error:
            logger.error(f"Failed to save error report: {save_error}")


# QA Agent Executor for orchestrator integration
class QAAgentExecutor:
    """QA Agent executor for orchestrator integration"""

    def __init__(self):
        self.qa_agent = QAAgent()

    async def execute_qa_agent(self, project_id: str, project_path: str,
                             specification_data: Dict[str, Any] = None,
                             architecture_data: Dict[str, Any] = None,
                             quality_level: str = "standard") -> Dict[str, Any]:
        """Execute QA agent as part of orchestrator workflow"""
        try:
            logger.info(f"Executing QA agent for project {project_id}")

            # Create QA context
            context = QAContext(
                project_path=Path(project_path),
                quality_level=QualityLevel(quality_level)
            )

            # Add specification if available
            if specification_data:
                context.project_specification = self._convert_specification_data(specification_data)

            # Add architecture if available
            if architecture_data:
                context.architecture = self._convert_architecture_data(architecture_data)

            # Execute QA process
            report = await self.qa_agent.execute_qa_process(context)

            return {
                "success": True,
                "project_id": project_id,
                "quality_score": report.quality_score,
                "overall_coverage": report.overall_coverage,
                "requirements_compliance": report.requirements_compliance,
                "security_score": report.security_score,
                "performance_score": report.performance_score,
                "test_suites": len(report.test_suites),
                "recommendations": report.recommendations,
                "report_path": str(context.project_path / "qa_reports")
            }

        except Exception as e:
            logger.error(f"QA agent execution failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "project_id": project_id
            }

    def _convert_specification_data(self, spec_data: Dict[str, Any]) -> ProjectSpecification:
        """Convert specification data to ProjectSpecification object"""
        # This would need to match the actual ProjectSpecification structure
        return ProjectSpecification(
            project_name=spec_data.get("project_name", "Unknown Project"),
            description=spec_data.get("description", "Generated project"),
            requirements=spec_data.get("requirements", {
                "functional": spec_data.get("functional_requirements", [])
            }),
            user_stories=spec_data.get("user_stories", []),
            technical_stack=spec_data.get("technical_stack", {}),
            architecture=spec_data.get("architecture", {}),
            constraints=spec_data.get("constraints", []),
            success_metrics=spec_data.get("success_metrics", []),
            risks=spec_data.get("risks", [])
        )

    def _convert_architecture_data(self, arch_data: Dict[str, Any]) -> SystemArchitecture:
        """Convert architecture data to SystemArchitecture object"""
        # This would need to match the actual SystemArchitecture structure
        # For now, create a basic implementation
        from architect_agent import ArchitecturePattern, ScalabilityTier

        return SystemArchitecture(
            project_name=arch_data.get("project_name", "Unknown Project"),
            architecture_pattern=ArchitecturePattern(arch_data.get("architecture_pattern", "layered")),
            scalability_tier=ScalabilityTier(arch_data.get("scalability_tier", "medium")),
            components=arch_data.get("components", []),
            data_architecture=arch_data.get("data_architecture", {}),
            security_architecture=arch_data.get("security_architecture", {}),
            deployment_architecture=arch_data.get("deployment_architecture", {}),
            integration_patterns=arch_data.get("integration_patterns", []),
            technology_stack=arch_data.get("technology_stack", {}),
            quality_attributes=arch_data.get("quality_attributes", {}),
            constraints=arch_data.get("constraints", []),
            assumptions=arch_data.get("assumptions", []),
            risks=arch_data.get("risks", [])
        )
