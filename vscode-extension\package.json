{"name": "aetherforge", "displayName": "Aetherforge", "description": "Autonomous AI Software Creation System", "version": "1.0.0", "publisher": "aetherforge", "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "activationEvents": [], "main": "./out/extension.js", "contributes": {"commands": [{"command": "aetherforge.createProject", "title": "Create Project", "category": "Aetherforge", "icon": "$(add)"}, {"command": "aetherforge.quickCreate", "title": "Quick Create Project", "category": "Aetherforge", "icon": "$(rocket)"}, {"command": "aetherforge.createFromTemplate", "title": "Create from Template", "category": "Aetherforge", "icon": "$(file-code)"}, {"command": "aetherforge.showAgentPanel", "title": "Agent Interaction Panel", "category": "Aetherforge", "icon": "$(robot)"}, {"command": "aetherforge.chatWithAgent", "title": "Chat with Agent", "category": "Aetherforge", "icon": "$(comment)"}, {"command": "aetherforge.viewAgentStatus", "title": "View Agent Status", "category": "Aetherforge", "icon": "$(pulse)"}, {"command": "aetherforge.showWorkflow", "title": "Workflow Visualization", "category": "Aetherforge", "icon": "$(git-branch)"}, {"command": "aetherforge.viewProjectStatus", "title": "View Project Status", "category": "Aetherforge", "icon": "$(project)"}, {"command": "aetherforge.showPheromoneTrail", "title": "Pheromone Trail", "category": "Aetherforge", "icon": "$(graph)"}, {"command": "aetherforge.showSystemStatus", "title": "System Status", "category": "Aetherforge", "icon": "$(server)"}, {"command": "aetherforge.openSettings", "title": "Settings", "category": "Aetherforge", "icon": "$(gear)"}, {"command": "aetherforge.refreshConnection", "title": "Refresh Connection", "category": "Aetherforge", "icon": "$(refresh)"}], "menus": {"commandPalette": [{"command": "aetherforge.createProject", "when": "true"}, {"command": "aetherforge.quickCreate", "when": "true"}, {"command": "aetherforge.showAgentPanel", "when": "true"}, {"command": "aetherforge.showWorkflow", "when": "true"}, {"command": "aetherforge.showSystemStatus", "when": "true"}], "explorer/context": [{"command": "aetherforge.createProject", "group": "aetherforge", "when": "explorerResourceIsFolder"}]}, "keybindings": [{"command": "aetherforge.createProject", "key": "ctrl+alt+a", "mac": "cmd+alt+a"}, {"command": "aetherforge.quickCreate", "key": "ctrl+alt+q", "mac": "cmd+alt+q"}, {"command": "aetherforge.showAgentPanel", "key": "ctrl+alt+g", "mac": "cmd+alt+g"}, {"command": "aetherforge.showWorkflow", "key": "ctrl+alt+w", "mac": "cmd+alt+w"}], "configuration": {"title": "Aetherforge", "properties": {"aetherforge.orchestratorUrl": {"type": "string", "default": "http://localhost:8000", "description": "URL of the Aetherforge orchestrator service"}, "aetherforge.autoOpenProjects": {"type": "boolean", "default": true, "description": "Automatically open generated projects in VS Code"}, "aetherforge.showNotifications": {"type": "boolean", "default": true, "description": "Show notifications for project creation and agent activities"}, "aetherforge.defaultProjectType": {"type": "string", "default": "fullstack", "enum": ["fullstack", "frontend", "backend", "mobile", "desktop", "api", "game"], "description": "Default project type for quick creation"}, "aetherforge.defaultAgentBehavior": {"type": "string", "default": "balanced", "enum": ["conservative", "balanced", "aggressive", "creative", "production"], "description": "Default agent behavior for project creation"}, "aetherforge.enableRealTimeUpdates": {"type": "boolean", "default": true, "description": "Enable real-time updates via WebSocket connections"}, "aetherforge.projectsDirectory": {"type": "string", "default": "./projects", "description": "Default directory for generated projects"}, "aetherforge.logLevel": {"type": "string", "default": "info", "enum": ["debug", "info", "warn", "error"], "description": "Logging level for the extension"}, "aetherforge.enableAdvancedFeatures": {"type": "boolean", "default": false, "description": "Enable advanced features like pheromone trail visualization"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "package": "vsce package", "install-extension": "code --install-extension *.vsix"}, "dependencies": {"axios": "^1.6.0"}, "devDependencies": {"@types/node": "16.x", "@types/vscode": "^1.74.0", "esbuild": "^0.19.12", "typescript": "^4.9.4"}}